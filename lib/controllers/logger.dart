import 'dart:io';
import 'dart:async';
import 'package:anki_guru/controllers/utils.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

/// Custom rotating file output for the logger package
class RotatingFileOutput extends LogOutput {
  final String baseFilePath;
  final int maxFileSizeBytes;
  final int maxFiles;
  final Duration rotateCheckInterval;

  File? _currentFile;
  IOSink? _sink;
  Timer? _rotateTimer;
  int _currentFileSize = 0;

  RotatingFileOutput({
    required this.baseFilePath,
    this.maxFileSizeBytes = 10 * 1024 * 1024, // 10MB default
    this.maxFiles = 5,
    this.rotateCheckInterval = const Duration(minutes: 5),
  }) {
    _initializeFile();
    _startRotateTimer();
  }

  void _initializeFile() {
    try {
      _currentFile = File(baseFilePath);
      if (_currentFile!.existsSync()) {
        _currentFileSize = _currentFile!.lengthSync();
      } else {
        _currentFile!.createSync(recursive: true);
        _currentFileSize = 0;
      }
      _sink = _currentFile!.openWrite(mode: FileMode.append);
    } catch (e) {
      print('Error initializing rotating file output: $e');
    }
  }

  void _startRotateTimer() {
    _rotateTimer = Timer.periodic(rotateCheckInterval, (_) {
      _checkAndRotate();
    });
  }

  void _checkAndRotate() {
    if (_currentFileSize >= maxFileSizeBytes) {
      _rotateFiles();
    }
  }

  void _rotateFiles() {
    try {
      // Close current sink
      _sink?.close();

      // Rotate existing files
      for (int i = maxFiles - 1; i >= 1; i--) {
        final oldFile = File('$baseFilePath.$i');
        final newFile = File('$baseFilePath.${i + 1}');

        if (oldFile.existsSync()) {
          if (i == maxFiles - 1) {
            // Delete the oldest file
            oldFile.deleteSync();
          } else {
            // Rename to next number
            oldFile.renameSync(newFile.path);
          }
        }
      }

      // Rename current file to .1
      if (_currentFile?.existsSync() == true) {
        _currentFile!.renameSync('$baseFilePath.1');
      }

      // Create new current file
      _initializeFile();

      print('Log file rotated. New file: $baseFilePath');
    } catch (e) {
      print('Error rotating log files: $e');
    }
  }

  @override
  void output(OutputEvent event) {
    try {
      if (_sink != null) {
        for (final line in event.lines) {
          _sink!.writeln(line);
          _currentFileSize += line.length + 1; // +1 for newline
        }
        _sink!.flush();

        // Check if rotation is needed after writing
        if (_currentFileSize >= maxFileSizeBytes) {
          _rotateFiles();
        }
      }
    } catch (e) {
      print('Error writing to rotating log file: $e');
    }
  }

  /// Get all available log files, starting from the most current one
  List<File> getAllLogFiles() {
    final files = <File>[];

    // Add current file if it exists
    if (_currentFile?.existsSync() == true) {
      files.add(_currentFile!);
    }

    // Add rotated files
    for (int i = 1; i <= maxFiles; i++) {
      final file = File('$baseFilePath.$i');
      if (file.existsSync()) {
        files.add(file);
      }
    }

    return files;
  }

  /// Force rotation regardless of file size
  void forceRotate() {
    _rotateFiles();
  }

  /// Close the output and clean up resources
  void dispose() {
    _rotateTimer?.cancel();
    _sink?.close();
  }
}

/// Configuration class for log rotation settings
class LogRotationConfig {
  final bool enabled;
  final int maxFileSizeBytes;
  final int maxFiles;
  final bool compressionEnabled;

  const LogRotationConfig({
    this.enabled = true,
    this.maxFileSizeBytes = 10 * 1024 * 1024, // 10MB default
    this.maxFiles = 5,
    this.compressionEnabled = false,
  });

  /// Create config from settings controller values
  factory LogRotationConfig.fromSettings({
    required bool enabled,
    required int maxFileSizeBytes,
    required int maxFiles,
    required bool compressionEnabled,
  }) {
    return LogRotationConfig(
      enabled: enabled,
      maxFileSizeBytes: maxFileSizeBytes,
      maxFiles: maxFiles,
      compressionEnabled: compressionEnabled,
    );
  }

  /// Validate configuration values
  bool get isValid {
    return maxFileSizeBytes > 0 &&
           maxFiles > 0 &&
           maxFiles <= 100 && // Reasonable upper limit
           maxFileSizeBytes >= 1024 * 1024; // At least 1MB
  }

  /// Get human-readable file size
  String get maxFileSizeFormatted {
    if (maxFileSizeBytes >= 1024 * 1024 * 1024) {
      return '${(maxFileSizeBytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
    } else if (maxFileSizeBytes >= 1024 * 1024) {
      return '${(maxFileSizeBytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    } else {
      return '${(maxFileSizeBytes / 1024).toStringAsFixed(1)}KB';
    }
  }

  @override
  String toString() {
    return 'LogRotationConfig(enabled: $enabled, maxSize: $maxFileSizeFormatted, maxFiles: $maxFiles, compression: $compressionEnabled)';
  }
}

class LoggerService {
  static final LoggerService _instance = LoggerService._internal();
  static late Logger logger;
  static File? _logFile;
  static RotatingFileOutput? _rotatingOutput;

  factory LoggerService() {
    return _instance;
  }

  LoggerService._internal();

  static Future<void> init([LogRotationConfig? rotationConfig]) async {
    // Add a small delay to ensure Flutter engine is fully initialized
    await Future.delayed(const Duration(milliseconds: 100));

    var directory = "";
    bool useFileLogging = true;

    if (Platform.isAndroid) {
      // Try to get external storage directory with fallback options
      try {
        directory = (await getExternalStorageDirectory())?.path ?? '';
        print("Android: getExternalStorageDirectory succeeded: $directory");
      } catch (e) {
        print("Android: getExternalStorageDirectory failed: $e");
        useFileLogging = false;
      }
    } else if (Platform.isIOS) {
      try {
        directory = (await getApplicationSupportDirectory()).path;
      } catch (e) {
        useFileLogging = false;
        print("Warning: Failed to get iOS application support directory, disabling file logging: $e");
      }
    } else if (Platform.isMacOS) {
      directory = p.join(Platform.environment['HOME'] ?? '', '.pdf_guru');
    } else if (Platform.isWindows) {
      directory =
          p.join(Platform.environment['USERPROFILE'] ?? '', '.pdf_guru');
    } else if (Platform.isLinux) {
      directory = p.join(Platform.environment['HOME'] ?? '', '.pdf_guru');
    }

    // Only set up file logging if we have a valid directory
    if (useFileLogging && directory.isNotEmpty) {
      try {
        _logFile = File(PathUtils.join([directory, 'app.log']));
        if (!await _logFile!.exists()) {
          await _logFile!.create(recursive: true);
        }
        print("Logger initialized with file: ${_logFile!.path}");
      } catch (e) {
        print("Warning: Failed to create log file, disabling file logging: $e");
        _logFile = null;
      }
    } else {
      _logFile = null;
      print("Logger initialized without file logging");
    }

    // Create logger with appropriate output based on file availability and rotation config
    if (_logFile != null) {
      // Use default rotation config if none provided
      final config = rotationConfig ?? const LogRotationConfig();

      if (config.enabled && config.isValid) {
        // Use rotating file output for log rotation
        try {
          _rotatingOutput = RotatingFileOutput(
            baseFilePath: _logFile!.path,
            maxFileSizeBytes: config.maxFileSizeBytes,
            maxFiles: config.maxFiles,
            rotateCheckInterval: const Duration(minutes: 5),
          );

          // File logging available with rotation - use both console and rotating file output
          logger = Logger(
            filter: MyFilter(),
            output: MultiOutput([
              ConsoleOutput(),
              _rotatingOutput!,
            ]),
            printer: PrettyPrinter(
              methodCount: 2,
              errorMethodCount: 8,
              lineLength: 120,
              colors: true,
              printEmojis: true,
            ),
            level: Level.all,
          );
          print("Logger initialized with rotation: ${config.toString()}");
        } catch (e) {
          print("Warning: Failed to create rotating file appender, falling back to regular file logging: $e");
          // Fall back to regular file logging
          logger = Logger(
            filter: MyFilter(),
            output: MultiOutput([
              ConsoleOutput(),
              FileOutput(file: _logFile!),
            ]),
            printer: PrettyPrinter(
              methodCount: 2,
              errorMethodCount: 8,
              lineLength: 120,
              colors: true,
              printEmojis: true,
            ),
            level: Level.all,
          );
        }
      } else {
        // File logging available without rotation - use both console and file output
        logger = Logger(
          filter: MyFilter(),
          output: MultiOutput([
            ConsoleOutput(),
            FileOutput(file: _logFile!),
          ]),
          printer: PrettyPrinter(
            methodCount: 2,
            errorMethodCount: 8,
            lineLength: 120,
            colors: true,
            printEmojis: true,
          ),
          level: Level.all,
        );
        print("Logger initialized without rotation (disabled or invalid config)");
      }
    } else {
      // File logging not available - use console output only
      logger = Logger(
        filter: MyFilter(),
        output: ConsoleOutput(),
        printer: PrettyPrinter(
          methodCount: 2,
          errorMethodCount: 8,
          lineLength: 120,
          colors: true,
          printEmojis: true,
        ),
        level: Level.all,
      );
    }

    logger.i("Logger initialized at ${DateTime.now()}");
    logger.d("Log file path: ${_logFile!.path}");
  }

  /// 关闭日志系统，确保所有日志都被写入文件
  static Future<void> close() async {
    // 写入最后一条日志
    logger.i("Logger closed at ${DateTime.now()}");

    // 关闭旋转文件输出
    try {
      _rotatingOutput?.dispose();
    } catch (e) {
      logger.e("Error disposing rotating output: $e");
    }

    // 刷新文件缓冲区
    try {
      // 对于某些日志实现，可能需要显式关闭
      // 这里我们尝试刷新文件缓冲区
      if (_logFile != null) {
        final sink = _logFile!.openWrite(mode: FileMode.append);
        await sink.flush();
        await sink.close();
      }
    } catch (e) {
      // Use logger instead of print for consistency
      logger.e("Error closing log file: $e");
    }
  }

  /// Get all available log files (current and rotated)
  static List<File> getAllLogFiles() {
    return _rotatingOutput?.getAllLogFiles() ?? [];
  }

  /// Force log rotation
  static void forceRotate() {
    _rotatingOutput?.forceRotate();
  }

  /// Update logger configuration with rotation settings
  /// This should be called after SettingController is initialized
  static Future<void> updateRotationConfig(LogRotationConfig config) async {
    if (_logFile == null) {
      logger.w("Cannot update rotation config: file logging is not available");
      return;
    }

    logger.i("Updating logger rotation configuration: ${config.toString()}");

    try {
      // Dispose existing rotating output if any
      _rotatingOutput?.dispose();
      _rotatingOutput = null;

      if (config.enabled && config.isValid) {
        // Create new rotating output with updated config
        _rotatingOutput = RotatingFileOutput(
          baseFilePath: _logFile!.path,
          maxFileSizeBytes: config.maxFileSizeBytes,
          maxFiles: config.maxFiles,
          rotateCheckInterval: const Duration(minutes: 5),
        );

        // Recreate logger with new rotating output
        logger = Logger(
          filter: MyFilter(),
          output: MultiOutput([
            ConsoleOutput(),
            _rotatingOutput!,
          ]),
          printer: PrettyPrinter(
            methodCount: 2,
            errorMethodCount: 8,
            lineLength: 120,
            colors: true,
            printEmojis: true,
          ),
          level: Level.all,
        );

        logger.i("Logger rotation configuration updated successfully");
      } else {
        // Disable rotation - use regular file output
        logger = Logger(
          filter: MyFilter(),
          output: MultiOutput([
            ConsoleOutput(),
            FileOutput(file: _logFile!),
          ]),
          printer: PrettyPrinter(
            methodCount: 2,
            errorMethodCount: 8,
            lineLength: 120,
            colors: true,
            printEmojis: true,
          ),
          level: Level.all,
        );

        logger.i("Logger rotation disabled");
      }
    } catch (e) {
      logger.e("Failed to update logger rotation configuration: $e");
    }
  }
}

class MyFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    return true;
  }
}

// 导出全局访问点
Logger get logger => LoggerService.logger;
