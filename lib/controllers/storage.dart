import 'package:hive/hive.dart';
import 'package:path_provider/path_provider.dart';

class InternalModelName {
  // 图片挖空模版
  static const String KEVIN_IMAGE_CLOZE = 'Kevin Image Cloze v5';
}

/// Storage names used across the app
class StorageBox {
  static const String imageCard = 'image_card';
  static const String videoNotes = 'video_notes';
  static const String ankiSync = 'anki_sync';
  static const String default_ = 'default';
  static const String pdfNote = 'pdf_note';
}

/// Storage keys for Anki preferences
class AnkiStorageKeys {
  static const String cardMode = 'card_mode';
  static const String outputDir = 'output_dir';
  static const String ankiConnectUrl = "ankiConnectUrl";
  static const String isLaunchAtStartup = "is_launch_at_startup";
  static const String ffmpegPath = "ffmpeg_path";
  static const String themeMode = "light";
  static const String ankiPath = "anki_path";
  static const String autoStartAnki = "auto_start_anki";
  static const String pdfReaderPath = "pdf_reader_path";
  static const String language = "language";
  // Log rotation settings
  static const String logRotationEnabled = "log_rotation_enabled";
  static const String logMaxFileSize = "log_max_file_size";
  static const String logMaxFiles = "log_max_files";
  static const String logCompressionEnabled = "log_compression_enabled";
}

/// Storage keys for AnkiSync preferences
class AnkiSyncKeys {
  static const String username = 'username';
  static const String password = 'password';
  static const String host = "host";
  static const String port = "port";
  static const String dataDir = "data_dir";
  static const String maxPayload = "max_payload";
  static const String userList = "user_list";
}

/// Storage keys for Image Card preferences
class ImageCardStorageKeys {
  static const String defaultDeck = 'default_deck';
  static const String defaultTags = 'default_tags';
  static const String defaultClozeMode = 'default_cloze_mode';
  static const String oneClozePerCard = 'one_cloze_per_card';
  static const String snapHotKey = 'snap_hotkey';
  static const String pasteHotKey = 'paste_hotkey';
  static const String autoOCR = 'auto_ocr';
  static const String primaryColor = 'primary_color';
  static const String secondaryColor = 'secondary_color';
}

/// Storage keys for Video Notes preferences
class VideoNotesStorageKeys {
  // Player settings
  static const String defaultPlayer = 'default_player';

  // Playback controls
  static const String longPressSpeedUp = 'long_press_speed_up';
  static const String defaultVideoSpeed = 'default_video_speed';
  static const String showMiniProgressBar = 'show_mini_progress_bar';
  static const String enableDoubleTapPause = 'enableDoubleTapPause';
  static const String enableDoubleTapSeek = 'enableDoubleTapSeek';
  static const String seekSeconds = 'seekSeconds';
  static const String enableBrightnessGesture = 'enableBrightnessGesture';
  static const String enableVolumeGesture = 'enableVolumeGesture';

  // Link settings
  static const String screenshotWithLink = 'screenshot_with_link';
  static const String linkPosition = 'link_position';
  static const String linkFormat = 'link_format';
  static const String customLinkTemplate = 'custom_link_format';
  static const String pauseAfterScreenshot = 'pause_after_screenshot';
  static const String pauseAfterCopyLink = 'pause_after_copy_link';
  static const String usePathEncoding = 'use_path_encoding';
  static const String useRelativePath = 'use_relative_path';
  static const String isAutoPaste = 'is_auto_paste';
  static const String rootDir = 'root_dir';

  // Anki settings
  static const String defaultDeck = 'default_deck';
  static const String defaultTags = 'default_tags';
  static const String defaultClozeMode = 'default_cloze_mode';
  static const String oneClozePeCard = 'one_cloze_per_card';
}

/// Centralized storage manager
class StorageManager {
  static final StorageManager _instance = StorageManager._internal();
  factory StorageManager() => _instance;
  StorageManager._internal();

  late final Box defaultStorage;
  late final Box imageCardStorage;
  late final Box videoNotesStorage;
  late final Box ankiSyncStorage;
  late final Box pdfNoteStorage;
  late final String storagePath;

  /// Initialize all storage instances
  Future<void> init() async {
    // Get the application support directory
    final appSupportDir = await getApplicationSupportDirectory();
    Hive.defaultDirectory = appSupportDir.path;

    // Open Hive boxes with specific paths
    defaultStorage = Hive.box(
      name: StorageBox.default_,
    );

    imageCardStorage = Hive.box(
      name: StorageBox.imageCard,
    );

    videoNotesStorage = Hive.box(
      name: StorageBox.videoNotes,
    );
    ankiSyncStorage = Hive.box(
      name: StorageBox.ankiSync,
    );
    pdfNoteStorage = Hive.box(
      name: StorageBox.pdfNote,
    );
  }

  /// Get storage instance by name
  Box getStorage(String name) {
    switch (name) {
      case StorageBox.default_:
        return defaultStorage;
      case StorageBox.imageCard:
        return imageCardStorage;
      case StorageBox.videoNotes:
        return videoNotesStorage;
      case StorageBox.ankiSync:
        return ankiSyncStorage;
      case StorageBox.pdfNote:
        return pdfNoteStorage;
      default:
        throw Exception('Unknown storage name: $name');
    }
  }

  /// Read value from storage with default value
  T read<T>(String box, String key, [T? defaultValue]) {
    final value = getStorage(box).get(key, defaultValue: defaultValue);
    return value;
  }

  /// Write value to storage
  void write(String box, String key, dynamic value) {
    getStorage(box).put(key, value);
  }

  /// Remove value from storage
  void remove(String box, String key) {
    getStorage(box).delete(key);
  }

  /// Clear all data in a storage box
  void clear(String box) {
    getStorage(box).clear();
  }
}
